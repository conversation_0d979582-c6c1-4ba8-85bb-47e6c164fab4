#!/bin/bash

# Kitco Research AI - Start Convenience Script (Unix/macOS)
# This script provides a convenient way to start the application on Unix-like systems

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if Python 3.12 is available (recommended)
if command -v python3.12 &> /dev/null; then
    PYTHON_CMD="python3.12"
elif command -v python3.11 &> /dev/null; then
    PYTHON_CMD="python3.11"
elif command -v python3.10 &> /dev/null; then
    PYTHON_CMD="python3.10"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    echo "❌ No Python 3 installation found"
    exit 1
fi

echo "🚀 Starting Kitco Research AI..."
exec "$PYTHON_CMD" scripts/setup/setup_cross_platform.py start "$@"
