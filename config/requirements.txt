# ============================================================================
# Kitco Research AI - Comprehensive Requirements File
# ============================================================================
# This file contains all Python dependencies for the Kitco Research AI project
# organized by category for better maintainability and understanding.
#
# Installation: pip install -r config/requirements.txt
# Update: pip freeze > config/requirements.txt (after installing new packages)
# ============================================================================

# ============================================================================
# CORE WEB FRAMEWORK & SERVER
# ============================================================================
Flask==3.1.1                    # Main web framework
flask-cors==6.0.0               # Cross-Origin Resource Sharing support
Flask-SocketIO==5.5.1           # Real-time bidirectional communication
Flask-WTF==1.2.2                # Form handling and CSRF protection
Werkzeug==3.1.3                 # WSGI utility library
WTForms==3.2.1                  # Form validation and rendering
Jinja2==3.1.6                   # Template engine
MarkupSafe==3.0.2               # Safe string handling for templates
itsdangerous==2.2.0             # Cryptographic signing
blinker==1.9.0                  # Signal/event system
click==8.2.1                    # Command line interface creation

# ============================================================================
# REAL-TIME COMMUNICATION
# ============================================================================
python-socketio==5.13.0         # Socket.IO server implementation
python-engineio==4.12.1         # Engine.IO server implementation
simple-websocket==1.1.0         # WebSocket implementation
bidict==0.23.1                  # Bidirectional dictionary
wsproto==1.2.0                  # WebSocket protocol implementation

# ============================================================================
# AI & MACHINE LEARNING
# ============================================================================
# LangChain Framework
langchain==0.3.25               # Main LangChain framework
langchain-community==0.3.24     # Community integrations
langchain-core==0.3.60          # Core LangChain components
langchain-ollama==0.3.3         # Ollama integration
langchain-openai==0.3.17        # OpenAI integration
langchain-text-splitters==0.3.8 # Text splitting utilities
langsmith==0.3.42               # LangSmith tracing and monitoring

# OpenAI & LLM Providers
openai==1.68.2                  # OpenAI API client
ollama==0.4.9                   # Ollama local LLM client
tiktoken==0.9.0                 # OpenAI tokenizer

# Machine Learning & NLP
torch==2.7.0                    # PyTorch deep learning framework
transformers==4.52.3            # Hugging Face transformers
sentence-transformers==4.1.0    # Sentence embeddings
tokenizers==0.21.1              # Fast tokenizers
huggingface-hub==0.32.2         # Hugging Face model hub
safetensors==0.5.3              # Safe tensor serialization

# Vector Search & Embeddings
faiss-cpu==1.11.0               # Facebook AI Similarity Search (CPU version)

# Scientific Computing
numpy==2.1.3                    # Numerical computing (compatible with ydata-profiling)
scipy==1.15.3                   # Scientific computing
pandas==2.2.3                   # Data manipulation and analysis
scikit-learn==1.6.1             # Machine learning library
matplotlib==3.10.0              # Plotting library (compatible with ydata-profiling)
plotly==6.1.1                   # Interactive plotting
sympy==1.14.0                   # Symbolic mathematics

# Natural Language Processing
nltk==3.9.1                     # Natural Language Toolkit
langdetect==1.0.9               # Language detection
python-iso639==2025.2.18        # ISO 639 language codes

# Optimization & Hyperparameter Tuning
optuna==4.3.0                   # Hyperparameter optimization

# ============================================================================
# DATABASE & DATA PERSISTENCE
# ============================================================================
SQLAlchemy==2.0.41              # SQL toolkit and ORM
alembic==1.16.1                 # Database migration tool
Mako==1.3.10                    # Template library (used by Alembic)

# ============================================================================
# WEB SCRAPING & SEARCH
# ============================================================================
playwright==1.52.0              # Browser automation for web scraping
beautifulsoup4==4.13.4          # HTML/XML parsing
lxml==5.4.0                     # XML and HTML processing
lxml_html_clean==0.4.2          # HTML cleaning utilities
html5lib==1.1                   # HTML5 parser
soupsieve==2.7                  # CSS selector library
duckduckgo_search==8.0.2        # DuckDuckGo search API
google_search_results==2.4.2    # Google search API (SerpAPI)
wikipedia==1.4.0                # Wikipedia API wrapper
feedparser==6.0.11              # RSS/Atom feed parser
arxiv==2.2.0                    # arXiv API wrapper

# ============================================================================
# HTTP CLIENTS & NETWORKING
# ============================================================================
requests==2.32.3                # HTTP library
requests-toolbelt==1.0.0        # Utilities for requests
httpx==0.28.1                   # Async HTTP client
httpcore==1.0.9                 # HTTP core functionality
httpx-sse==0.4.0                # Server-Sent Events for httpx
aiohttp==3.12.2                 # Async HTTP client/server
aiosignal==1.3.2                # Async signal handling
aiohappyeyeballs==2.6.1         # Happy Eyeballs for aiohttp
urllib3==2.4.0                  # HTTP client
certifi==2025.4.26              # Certificate bundle
charset-normalizer==3.4.2       # Character encoding detection
idna==3.10                      # Internationalized domain names

# ============================================================================
# DOCUMENT PROCESSING
# ============================================================================
# PDF Processing
pypdf==5.5.0                    # PDF manipulation
pdfplumber==0.11.6              # PDF text extraction
pdfminer.six==20250327          # PDF mining and analysis
pypdfium2==4.30.1               # PDF rendering

# Document Analysis & Unstructured Data
unstructured==0.17.2            # Unstructured document processing
unstructured-client==0.28.1     # Client for unstructured API (compatible with pydantic 2.9.2)
filetype==1.2.0                 # File type detection
python-magic==0.4.27            # File type identification
python-oxmsg==0.0.2             # Outlook message file processing

# Text Processing
jusText==3.0.2                  # Text extraction from HTML
RapidFuzz==3.13.0               # Fast string matching
regex==2024.11.6                # Advanced regular expressions

# ============================================================================
# CONFIGURATION & ENVIRONMENT
# ============================================================================
dynaconf==3.2.11                # Configuration management
python-dotenv==1.1.0            # Environment variable loading
pydantic==2.9.2                 # Data validation using type hints (compatible with safety-schemas)
pydantic-settings==2.9.1        # Settings management with Pydantic
pydantic_core==2.23.4           # Core validation logic for Pydantic (compatible with pydantic 2.9.2)
PyYAML==6.0.2                   # YAML parser and emitter
toml==0.10.2                    # TOML parser

# ============================================================================
# LOGGING & MONITORING
# ============================================================================
loguru==0.7.3                   # Advanced logging
colorlog==6.9.0                 # Colored log output

# ============================================================================
# ASYNC & CONCURRENCY
# ============================================================================
# Note: asyncio is built-in to Python 3.7+, no need to install
anyio==4.9.0                    # Async compatibility layer
nest-asyncio==1.6.0             # Nested asyncio event loops
greenlet==3.2.2                 # Lightweight coroutines

# ============================================================================
# CACHING & PERFORMANCE
# ============================================================================
backoff==2.2.1                  # Exponential backoff for retries
tenacity==9.1.2                 # Retry library
methodtools==0.4.7              # Method utilities and decorators

# ============================================================================
# UTILITIES & HELPERS
# ============================================================================
# File & I/O Operations
aiofiles==24.1.0                # Async file operations
filelock==3.12.4                # File locking (compatible with safety package)
fsspec==2025.5.1                # File system specification
platformdirs==4.3.8             # Platform-specific directories

# Data Structures & Serialization
attrs==25.3.0                   # Classes without boilerplate
dataclasses-json==0.6.7         # JSON serialization for dataclasses
marshmallow==3.26.1             # Object serialization/deserialization
orjson==3.10.18                 # Fast JSON library
jsonpatch==1.33                 # JSON patch implementation
jsonpointer==3.0.0              # JSON pointer implementation

# Text & String Processing
six==1.17.0                     # Python 2/3 compatibility
emoji==2.14.1                   # Emoji handling
webencodings==0.5.1             # Web encoding utilities
xmltodict==0.14.2               # XML to dictionary conversion

# System & Process Management
psutil==6.0.0                   # System and process utilities (compatible with safety package)
distro==1.9.0                   # Linux distribution detection

# Compression & Archives
zstandard==0.23.0               # Zstandard compression

# Network & URLs
yarl==1.20.0                    # URL manipulation
multidict==6.4.4                # Multi-value dictionary
frozenlist==1.6.0               # Immutable list implementation
propcache==0.3.1                # Property caching

# Cryptography & Security
cryptography==45.0.3            # Cryptographic recipes and primitives
cffi==1.17.1                    # C Foreign Function Interface

# ============================================================================
# SEARCH & INDEXING
# ============================================================================
elasticsearch==8.14.0           # Elasticsearch client
elastic-transport==8.17.1       # Elasticsearch transport layer

# ============================================================================
# TESTING & DEVELOPMENT
# ============================================================================
pytest==8.3.5                   # Testing framework
pluggy==1.6.0                   # Plugin system for pytest
iniconfig==2.1.0                # INI configuration parser
packaging==24.2                 # Core utilities for Python packages

# ============================================================================
# TYPE CHECKING & ANNOTATIONS
# ============================================================================
typing_extensions==4.13.2       # Backported type hints
annotated-types==0.7.0          # Annotated types support
typing-inspect==0.9.0           # Runtime type inspection
typing-inspection==0.4.1        # Type inspection utilities
mypy_extensions==1.1.0          # Extensions for mypy

# ============================================================================
# SCIENTIFIC & MATHEMATICAL LIBRARIES
# ============================================================================
# Core Scientific Stack
joblib==1.5.1                   # Parallel computing utilities
threadpoolctl==3.6.0            # Thread pool control
tqdm==4.67.1                    # Progress bars
networkx==3.4.2                 # Network analysis
contourpy==1.3.2                # Contour plotting
cycler==0.12.1                  # Composable style cycles
fonttools==4.58.0               # Font tools
kiwisolver==1.4.8               # Fast implementation of Cassowary constraint solver
mpmath==1.3.0                   # Arbitrary-precision arithmetic
pyparsing==3.2.3                # Parsing library

# Data Processing
narwhals==1.41.0                # DataFrame interchange protocol
olefile==0.47                   # OLE file parser

# ============================================================================
# LEGACY & COMPATIBILITY
# ============================================================================
sgmllib3k==1.0.0                # SGML parser (Python 3 port)
setuptools==80.8.0              # Package development utilities
importlib_resources==6.5.2      # Resource access (backport)
wrapt==1.17.2                   # Decorator utilities
wirerope==1.0.0                 # Wire protocol utilities

# ============================================================================
# TIME & DATE HANDLING
# ============================================================================
python-dateutil==2.9.0.post0    # Date/time utilities
pytz==2025.2                    # Timezone definitions
tzdata==2025.2                  # Timezone database

# ============================================================================
# BROWSER AUTOMATION SUPPORT
# ============================================================================
pyee==13.0.0                    # Event emitter (used by Playwright)

# ============================================================================
# ENCODING & CHARACTER DETECTION
# ============================================================================
chardet==5.2.0                  # Character encoding detection

# ============================================================================
# HTTP/WEB PROTOCOL SUPPORT
# ============================================================================
h11==0.16.0                     # HTTP/1.1 protocol implementation
sniffio==1.3.1                  # Async library detection

# ============================================================================
# SPECIALIZED UTILITIES
# ============================================================================
hf-xet==1.1.2                   # Hugging Face XET utilities
primp==0.15.0                   # HTTP client utilities
