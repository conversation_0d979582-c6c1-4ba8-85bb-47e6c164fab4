#!/bin/bash

# Kitco Research AI - Setup Convenience Script (Unix/macOS)
# This script provides a convenient way to run setup on Unix-like systems

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if Python 3.12 is available (recommended)
if command -v python3.12 &> /dev/null; then
    echo "🐍 Using Python 3.12 (recommended)"
    PYTHON_CMD="python3.12"
elif command -v python3.11 &> /dev/null; then
    echo "🐍 Using Python 3.11"
    PYTHON_CMD="python3.11"
elif command -v python3.10 &> /dev/null; then
    echo "🐍 Using Python 3.10"
    PYTHON_CMD="python3.10"
elif command -v python3 &> /dev/null; then
    echo "⚠️  Using python3 (may have compatibility issues with Python 3.13+)"
    PYTHON_CMD="python3"
else
    echo "❌ No Python 3 installation found"
    exit 1
fi

echo "🚀 Running Kitco Research AI setup..."
exec "$PYTHON_CMD" scripts/setup/setup_cross_platform.py setup "$@"
